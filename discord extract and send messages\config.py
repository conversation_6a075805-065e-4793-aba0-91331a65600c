# Discord Bot Configuration
# Replace these values with your actual bot token and settings

# Bot Token - Get this from Discord Developer Portal
BOT_TOKEN = "MTM4MDk0ODU4NzI4NTk3NTA3Mg.GYHz3y.rkzABQ4pWBzu4f2UktpWRAHmoeyjMZV3z3YFFs"

# Channel ID where you want to extract users from
# Right-click on the channel and select "Copy ID" (Developer Mode must be enabled)
CHANNEL_ID = 1054049526773731329  # Replace with actual channel ID

# Message to send to users
DM_MESSAGE = """
Hello! 👋

I hope you're having a great day! I wanted to reach out because I noticed you're active in our community.

[Your custom message here]

Best regards!
"""

# Maximum number of messages to scan in the channel (None = all messages)
# Be careful with large channels as this can take a long time
MESSAGE_LIMIT = 1000  # Set to None for all messages, or a number like 1000

# Delay between sending DMs (in seconds) to avoid rate limiting
# Discord allows 1 DM per second, but 2-3 seconds is safer
DELAY_BETWEEN_DMS = 3

# Bot owner ID (your Discord user ID) - only this user can run commands
# Right-click on your username and select "Copy ID"
BOT_OWNER_ID = 123456789012345678  # Replace with your Discord user ID

# Additional settings
MAX_RETRIES = 3  # Number of retries for failed DMs
SAVE_PROGRESS = True  # Save progress to file
LOG_LEVEL = "INFO"  # Logging level: DEBUG, INFO, WARNING, ERROR
