import discord
from discord.ext import commands
import asyncio
import logging
from datetime import datetime, timedelta
import json
import os
from config import BOT_TOKEN, CHANNEL_ID, DM_MESSAGE, MESSAGE_LIMIT, DELAY_BETWEEN_DMS, BOT_OWNER_ID

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Bot setup with necessary intents
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
bot = commands.Bot(command_prefix='!', intents=intents)

class DMBot:
    def __init__(self):
        self.sent_users = set()
        self.failed_users = []
        self.success_count = 0
        self.fail_count = 0
        
    async def extract_users_from_channel(self, channel_id, limit=None):
        """Extract unique users who have posted in the specified channel"""
        try:
            channel = bot.get_channel(channel_id)
            if not channel:
                logger.error(f"Channel with ID {channel_id} not found")
                return []
            
            logger.info(f"Extracting users from channel: {channel.name}")
            unique_users = set()
            
            # Get messages from the channel
            async for message in channel.history(limit=limit):
                if not message.author.bot:  # Skip bot messages
                    unique_users.add(message.author)
            
            logger.info(f"Found {len(unique_users)} unique users in channel")
            return list(unique_users)
            
        except discord.Forbidden:
            logger.error("Bot doesn't have permission to read the channel")
            return []
        except Exception as e:
            logger.error(f"Error extracting users: {str(e)}")
            return []
    
    async def send_dm_to_user(self, user, message):
        """Send a DM to a specific user"""
        try:
            # Check if we already sent to this user
            if user.id in self.sent_users:
                logger.info(f"Already sent DM to {user.name}, skipping")
                return True
            
            # Try to send the DM
            await user.send(message)
            self.sent_users.add(user.id)
            self.success_count += 1
            logger.info(f"✅ Successfully sent DM to {user.name}#{user.discriminator}")
            return True
            
        except discord.Forbidden:
            self.fail_count += 1
            self.failed_users.append(f"{user.name}#{user.discriminator} - DMs disabled/blocked")
            logger.warning(f"❌ Failed to send DM to {user.name}#{user.discriminator} - DMs disabled or blocked")
            return False
        except discord.HTTPException as e:
            self.fail_count += 1
            self.failed_users.append(f"{user.name}#{user.discriminator} - HTTP Error: {str(e)}")
            logger.error(f"❌ HTTP error sending DM to {user.name}#{user.discriminator}: {str(e)}")
            return False
        except Exception as e:
            self.fail_count += 1
            self.failed_users.append(f"{user.name}#{user.discriminator} - Error: {str(e)}")
            logger.error(f"❌ Unexpected error sending DM to {user.name}#{user.discriminator}: {str(e)}")
            return False
    
    async def send_dms_to_all_users(self, users, message, delay=2):
        """Send DMs to all users with rate limiting"""
        logger.info(f"Starting to send DMs to {len(users)} users")
        
        for i, user in enumerate(users, 1):
            logger.info(f"Processing user {i}/{len(users)}: {user.name}#{user.discriminator}")
            
            await self.send_dm_to_user(user, message)
            
            # Rate limiting - wait between sends
            if i < len(users):  # Don't wait after the last user
                logger.info(f"Waiting {delay} seconds before next DM...")
                await asyncio.sleep(delay)
        
        # Print summary
        logger.info(f"\n=== DM CAMPAIGN SUMMARY ===")
        logger.info(f"Total users processed: {len(users)}")
        logger.info(f"Successful DMs sent: {self.success_count}")
        logger.info(f"Failed DMs: {self.fail_count}")
        
        if self.failed_users:
            logger.info(f"\nFailed users:")
            for failed_user in self.failed_users:
                logger.info(f"  - {failed_user}")
    
    def save_results(self):
        """Save results to a JSON file"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_processed": self.success_count + self.fail_count,
            "successful": self.success_count,
            "failed": self.fail_count,
            "failed_users": self.failed_users,
            "sent_user_ids": list(self.sent_users)
        }
        
        with open('dm_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info("Results saved to dm_results.json")

# Initialize the DM bot
dm_bot = DMBot()

@bot.event
async def on_ready():
    logger.info(f'{bot.user} has logged in!')
    logger.info(f'Bot is in {len(bot.guilds)} guilds')

@bot.command(name='extract_and_dm')
async def extract_and_dm(ctx):
    """Extract users from channel and send them DMs"""
    if ctx.author.id != BOT_OWNER_ID:  # Only bot owner can use this command
        await ctx.send("You don't have permission to use this command.")
        return
    
    await ctx.send("🔍 Starting user extraction and DM campaign...")
    
    # Extract users from the specified channel
    users = await dm_bot.extract_users_from_channel(CHANNEL_ID, MESSAGE_LIMIT)
    
    if not users:
        await ctx.send("❌ No users found or couldn't access the channel.")
        return
    
    await ctx.send(f"📋 Found {len(users)} users. Starting DM campaign...")
    
    # Send DMs to all users
    await dm_bot.send_dms_to_all_users(users, DM_MESSAGE, DELAY_BETWEEN_DMS)
    
    # Save results
    dm_bot.save_results()
    
    await ctx.send(f"✅ DM campaign completed!\n"
                  f"Successful: {dm_bot.success_count}\n"
                  f"Failed: {dm_bot.fail_count}\n"
                  f"Check dm_results.json for detailed results.")

@bot.command(name='stats')
async def stats(ctx):
    """Show current campaign stats"""
    await ctx.send(f"📊 **Current Stats:**\n"
                  f"Successful DMs: {dm_bot.success_count}\n"
                  f"Failed DMs: {dm_bot.fail_count}\n"
                  f"Total processed: {dm_bot.success_count + dm_bot.fail_count}")

if __name__ == "__main__":
    try:
        bot.run(BOT_TOKEN)
    except Exception as e:
        logger.error(f"Failed to start bot: {str(e)}")
