# Self-Bot Configuration
# ⚠️ WARNING: Self-bots are against Discord Terms of Service
# Use at your own risk and for educational purposes only

# Your Discord User Token (NOT bot token)
# See instructions below on how to get this
USER_TOKEN = "MTI2ODcxMDQ3MDY4NTE2MzcwMg.G5Tspi.w06_jrqdCPx0tSSPqY7Cw1tvXDDWBvWyeGMOzU"

# Channel ID to extract users from
CHANNEL_ID = 123456789012345678

# Message to send
DM_MESSAGE = """
Hello! 👋

I noticed you're active in our community and wanted to reach out.

[Your message here]

Best regards!
"""

# Settings
MESSAGE_LIMIT = 1000  # Messages to scan
DELAY_BETWEEN_DMS = 5  # Seconds between DMs (higher for safety)

# HOW TO GET YOUR USER TOKEN:
# ⚠️ WARNING: Never share your user token with anyone!
# 
# Method 1 (Browser):
# 1. Open Discord in your web browser
# 2. Press F12 to open Developer Tools
# 3. Go to "Network" tab
# 4. Refresh the page or send a message
# 5. Look for requests to "api/v9" or similar
# 6. In the request headers, find "authorization"
# 7. Copy the token (it's a long string)
#
# Method 2 (Desktop App):
# 1. Open Discord desktop app
# 2. Press Ctrl+Shift+I (Windows) or Cmd+Option+I (Mac)
# 3. Go to "Network" tab
# 4. Send a message or refresh
# 5. Look for API requests and find the authorization header
#
# The token will look like:
# "mfa.abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ"
# or
# "MTIzNDU2Nzg5MDEyMzQ1Njc4.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYz"
