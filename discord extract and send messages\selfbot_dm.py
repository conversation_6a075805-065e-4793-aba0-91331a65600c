import discord
import asyncio
import logging
from datetime import datetime
import json
import os

# ⚠️ WARNING: Self-bots are against Discord ToS if used for automation
# Use this responsibly and at your own risk
# This is for educational purposes only

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('selfbot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration - Replace these values
USER_TOKEN = "MTI2ODcxMDQ3MDY4NTE2MzcwMg.G5Tspi.w06_jrqdCPx0tSSPqY7Cw1tvXDDWBvWyeGMOzU"  # Your Discord account token (NOT bot token)
CHANNEL_ID = 1054049526773731329  # Channel ID to extract users from
DM_MESSAGE = """
Hello! 👋

I hope you're having a great day! I noticed you're active in our community.

[Your custom message here]

Best regards!
"""
MESSAGE_LIMIT = 1000  # Number of messages to scan
DELAY_BETWEEN_DMS = 5  # Delay in seconds (higher for self-bots)

class SelfBotDM:
    def __init__(self):
        self.client = discord.Client()
        self.sent_users = set()
        self.failed_users = []
        self.success_count = 0
        self.fail_count = 0
        
        # Set up events
        self.client.event(self.on_ready)
    
    async def on_ready(self):
        logger.info(f'Logged in as {self.client.user}')
        logger.info(f'User ID: {self.client.user.id}')
        
        # Start the DM process
        await self.start_dm_process()
    
    async def extract_users_from_channel(self, channel_id, limit=None):
        """Extract unique users who have posted in the specified channel"""
        try:
            channel = self.client.get_channel(channel_id)
            if not channel:
                logger.error(f"Channel with ID {channel_id} not found or not accessible")
                return []
            
            logger.info(f"Extracting users from channel: {channel.name}")
            unique_users = set()
            
            # Get messages from the channel
            async for message in channel.history(limit=limit):
                if not message.author.bot and message.author.id != self.client.user.id:
                    unique_users.add(message.author)
            
            logger.info(f"Found {len(unique_users)} unique users in channel")
            return list(unique_users)
            
        except discord.Forbidden:
            logger.error("No permission to read the channel")
            return []
        except Exception as e:
            logger.error(f"Error extracting users: {str(e)}")
            return []
    
    async def send_dm_to_user(self, user, message):
        """Send a DM to a specific user"""
        try:
            # Check if we already sent to this user
            if user.id in self.sent_users:
                logger.info(f"Already sent DM to {user.name}, skipping")
                return True
            
            # Try to send the DM
            await user.send(message)
            self.sent_users.add(user.id)
            self.success_count += 1
            logger.info(f"✅ Successfully sent DM to {user.name}")
            return True
            
        except discord.Forbidden:
            self.fail_count += 1
            self.failed_users.append(f"{user.name} - DMs disabled/blocked")
            logger.warning(f"❌ Failed to send DM to {user.name} - DMs disabled or blocked")
            return False
        except discord.HTTPException as e:
            self.fail_count += 1
            self.failed_users.append(f"{user.name} - HTTP Error: {str(e)}")
            logger.error(f"❌ HTTP error sending DM to {user.name}: {str(e)}")
            return False
        except Exception as e:
            self.fail_count += 1
            self.failed_users.append(f"{user.name} - Error: {str(e)}")
            logger.error(f"❌ Unexpected error sending DM to {user.name}: {str(e)}")
            return False
    
    async def send_dms_to_all_users(self, users, message, delay=5):
        """Send DMs to all users with rate limiting"""
        logger.info(f"Starting to send DMs to {len(users)} users")
        
        for i, user in enumerate(users, 1):
            logger.info(f"Processing user {i}/{len(users)}: {user.name}")
            
            await self.send_dm_to_user(user, message)
            
            # Rate limiting - wait between sends (important for self-bots)
            if i < len(users):
                logger.info(f"Waiting {delay} seconds before next DM...")
                await asyncio.sleep(delay)
        
        # Print summary
        logger.info(f"\n=== DM CAMPAIGN SUMMARY ===")
        logger.info(f"Total users processed: {len(users)}")
        logger.info(f"Successful DMs sent: {self.success_count}")
        logger.info(f"Failed DMs: {self.fail_count}")
        
        if self.failed_users:
            logger.info(f"\nFailed users:")
            for failed_user in self.failed_users:
                logger.info(f"  - {failed_user}")
    
    async def start_dm_process(self):
        """Main process to extract users and send DMs"""
        try:
            # Extract users from the specified channel
            users = await self.extract_users_from_channel(CHANNEL_ID, MESSAGE_LIMIT)
            
            if not users:
                logger.error("No users found or couldn't access the channel.")
                await self.client.close()
                return
            
            logger.info(f"Found {len(users)} users. Starting DM campaign...")
            
            # Send DMs to all users
            await self.send_dms_to_all_users(users, DM_MESSAGE, DELAY_BETWEEN_DMS)
            
            # Save results
            self.save_results()
            
            logger.info("DM campaign completed!")
            await self.client.close()
            
        except Exception as e:
            logger.error(f"Error in DM process: {str(e)}")
            await self.client.close()
    
    def save_results(self):
        """Save results to a JSON file"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "total_processed": self.success_count + self.fail_count,
            "successful": self.success_count,
            "failed": self.fail_count,
            "failed_users": self.failed_users,
            "sent_user_ids": list(self.sent_users)
        }
        
        with open('selfbot_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info("Results saved to selfbot_results.json")
    
    def run(self):
        """Start the self-bot"""
        try:
            self.client.run(USER_TOKEN, bot=False)  # bot=False for user accounts
        except Exception as e:
            logger.error(f"Failed to start self-bot: {str(e)}")

if __name__ == "__main__":
    # ⚠️ WARNING: Read the disclaimer at the top of this file
    selfbot = SelfBotDM()
    selfbot.run()
