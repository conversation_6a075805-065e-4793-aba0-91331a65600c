# Discord DM Bot

A Discord bot that extracts users from a specific channel and sends them direct messages.

## ⚠️ Important Disclaimer

**Use this bot responsibly and in compliance with Discord's Terms of Service.**
- Only send DMs to users who would reasonably expect to receive them
- Don't spam users or send unsolicited messages
- Respect users' privacy and Discord's anti-spam policies
- Consider getting consent before mass DMing

## Features

- 📋 Extract unique users who have posted in a specific channel
- 💬 Send custom DMs to all extracted users
- ⏱️ Built-in rate limiting to avoid Discord API limits
- 📊 Detailed logging and statistics
- 💾 Save results to JSON file
- 🔄 Skip users who have already been messaged
- ❌ Handle users with DMs disabled gracefully

## Setup Instructions

### 1. Create a Discord Bot

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name
3. Go to the "Bot" section
4. Click "Add Bot"
5. Copy the bot token (you'll need this later)
6. Enable the following bot permissions:
   - Send Messages
   - Read Message History
   - Send Messages in DMs

### 2. Invite <PERSON><PERSON> to Server

1. Go to the "OAuth2" > "URL Generator" section
2. Select "bot" scope
3. Select these permissions:
   - Send Messages
   - Read Message History
   - Use Slash Commands
4. Copy the generated URL and open it in your browser
5. Select the server and authorize the bot

### 3. Enable Developer Mode

1. In Discord, go to Settings > Advanced
2. Enable "Developer Mode"
3. Now you can right-click on channels/users to copy their IDs

### 4. Configure the Bot

1. Open `config.py`
2. Replace `YOUR_BOT_TOKEN_HERE` with your actual bot token
3. Replace the `CHANNEL_ID` with the ID of the channel you want to extract users from
4. Replace `BOT_OWNER_ID` with your Discord user ID
5. Customize the `DM_MESSAGE` with your desired message
6. Adjust other settings as needed

### 5. Install Dependencies

```bash
pip install -r requirements.txt
```

### 6. Run the Bot

```bash
python discord_dm_bot.py
```

## Usage

Once the bot is running and online in your server:

1. Use the command `!extract_and_dm` in any channel where the bot has access
2. The bot will:
   - Extract all unique users from the specified channel
   - Send them the configured DM message
   - Provide real-time updates on progress
   - Save results to `dm_results.json`

### Commands

- `!extract_and_dm` - Start the extraction and DM campaign
- `!stats` - Show current campaign statistics

## Configuration Options

Edit `config.py` to customize:

- `BOT_TOKEN` - Your Discord bot token
- `CHANNEL_ID` - Channel to extract users from
- `DM_MESSAGE` - Message to send to users
- `MESSAGE_LIMIT` - Max messages to scan (None = all)
- `DELAY_BETWEEN_DMS` - Seconds between each DM (recommended: 2-3)
- `BOT_OWNER_ID` - Only this user can run commands

## Rate Limiting

The bot includes built-in rate limiting:
- Default 3-second delay between DMs
- Respects Discord's API limits
- Logs all attempts and failures

## Output Files

- `bot.log` - Detailed bot activity log
- `dm_results.json` - Campaign results and statistics

## Troubleshooting

### Common Issues

1. **Bot can't see the channel**
   - Make sure the bot has "Read Message History" permission
   - Verify the channel ID is correct

2. **Many DMs failing**
   - Users may have DMs disabled from server members
   - Some users may have blocked the bot
   - This is normal and expected

3. **Rate limiting errors**
   - Increase `DELAY_BETWEEN_DMS` in config
   - Discord allows ~1 DM per second, but 2-3 seconds is safer

### Error Messages

- `Channel not found` - Check your CHANNEL_ID
- `Forbidden` - Bot lacks permissions
- `DMs disabled` - User has DMs turned off (normal)

## Legal and Ethical Considerations

- Only message users who would reasonably expect contact
- Respect Discord's Terms of Service
- Don't use for spam or harassment
- Consider privacy implications
- Get consent when possible

## Support

If you encounter issues:
1. Check the `bot.log` file for detailed error messages
2. Verify your configuration in `config.py`
3. Ensure the bot has proper permissions
4. Check Discord's status page for API issues
