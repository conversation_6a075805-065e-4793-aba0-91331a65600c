# Discord Bot Configuration Example
# Copy this file to config.py and fill in your actual values

# Bot Token - Get this from Discord Developer Portal
# 1. Go to https://discord.com/developers/applications
# 2. Create a new application or select existing one
# 3. Go to "Bot" section and copy the token
BOT_TOKEN = "MTM4MDk0ODU4NzI4NTk3NTA3Mg.GYHz3y.rkzABQ4pWBzu4f2UktpWRAHmoeyjMZV3z3YFFs"

# Channel ID where you want to extract users from
# 1. Enable Developer Mode in Discord (Settings > Advanced > Developer Mode)
# 2. Right-click on the channel and select "Copy ID"
CHANNEL_ID = 1054049526773731329

# Message to send to users
DM_MESSAGE = """
Hello! 👋

I hope you're having a great day! I noticed you're active in our community and wanted to reach out.

I'm working on [your project/reason] and thought you might be interested in [your offer/information].

Feel free to reply if you'd like to know more!

Best regards,
[Your name]
"""

# Maximum number of messages to scan in the channel
# None = scan all messages (can be slow for large channels)
# 1000 = scan last 1000 messages (recommended for testing)
MESSAGE_LIMIT = 1000

# Delay between sending DMs (in seconds)
# Discord rate limit is ~1 DM per second, but 2-3 seconds is safer
DELAY_BETWEEN_DMS = 3

# Bot owner ID (your Discord user ID)
# 1. Enable Developer Mode in Discord
# 2. Right-click on your username and select "Copy ID"
BOT_OWNER_ID = 1234567890123456789

# Additional settings
MAX_RETRIES = 3
SAVE_PROGRESS = True
LOG_LEVEL = "INFO"
