import discord
import asyncio

TOKEN = "MTI2ODcxMDQ3MDY4NTE2MzcwMg.G5Tspi.w06_jrqdCPx0tSSPqY7Cw1tvXDDWBvWyeGMOzU"  # Replace with your token but NEVER share it publicly
CHANNEL_ID = 1054058040208400484  # معرف القناة النصية
MESSAGE_TO_SEND = "Your message here"  # Add your message content here

# Set up intents for the Discord client
intents = discord.Intents.default()
intents.message_content = True
intents.guild_messages = True
intents.dm_messages = True

client = discord.Client(intents=intents)

@client.event
async def on_ready():
    print(f"✅ Logged in as {client.user}")

    channel = client.get_channel(CHANNEL_ID)

    if channel is None:
        print("❌ لم يتم العثور على القناة")
        return

    unique_users = set()

    async for message in channel.history(limit=200):
        if not message.author.bot and message.author.id != client.user.id:
            unique_users.add(message.author)

    print(f"📌 Found {len(unique_users)} unique users.")

    for user in unique_users:
        try:
            await user.send(MESSAGE_TO_SEND)
            print(f"✅ Message sent to {user.name}")
            await asyncio.sleep(3)  # لتجنب الحظر من Discord
        except Exception as e:
            print(f"❌ Failed to send message to {user.name}: {e}")

    await client.close()

client.run(TOKEN)
