#!/usr/bin/env python3
"""
Setup script for Discord DM Bot
This script helps you set up the bot configuration
"""

import os
import shutil

def setup_config():
    """Setup configuration file"""
    print("🔧 Discord DM Bot Setup")
    print("=" * 40)
    
    # Check if config.py already exists
    if os.path.exists('config.py'):
        response = input("config.py already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return
    
    # Copy example config
    if os.path.exists('config_example.py'):
        shutil.copy('config_example.py', 'config.py')
        print("✅ Created config.py from example")
    else:
        print("❌ config_example.py not found")
        return
    
    print("\n📝 Please edit config.py and fill in the following:")
    print("1. BOT_TOKEN - Get from Discord Developer Portal")
    print("2. CHANNEL_ID - Right-click channel and 'Copy ID'")
    print("3. BOT_OWNER_ID - Right-click your username and 'Copy ID'")
    print("4. DM_MESSAGE - Customize your message")
    
    print("\n📚 See README.md for detailed setup instructions")
    print("\n🚀 After configuration, run: python discord_dm_bot.py")

def check_dependencies():
    """Check if required packages are installed"""
    try:
        import discord
        print("✅ discord.py is installed")
    except ImportError:
        print("❌ discord.py not found. Install with: pip install -r requirements.txt")
        return False
    return True

if __name__ == "__main__":
    print("Checking dependencies...")
    if check_dependencies():
        setup_config()
    else:
        print("\nPlease install dependencies first:")
        print("pip install -r requirements.txt")
